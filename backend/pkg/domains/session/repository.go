package session

import (
	"context"
	"errors"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"gorm.io/gorm"
)

// Repository defines the interface for session-related database operations
type Repository interface {
	CreateSession(ctx context.Context, session entities.Session) (entities.Session, error)
	GetSessionByID(ctx context.Context, id uuid.UUID) (entities.Session, error)
	GetSessionByRegID(ctx context.Context, regID string) (entities.Session, error)
	// UpdateSession updates a session
	UpdateSession(ctx context.Context, session entities.Session) error
	// DeleteSession deletes a session
	DeleteSession(ctx context.Context, id uuid.UUID) error
	// ListSessions lists all sessions with optional filtering
	ListSessions(ctx context.Context, status string, limit, offset int) ([]entities.Session, int64, error)
	// RecordSessionEvent records a new session event
	RecordSessionEvent(ctx context.Context, event entities.SessionEvent) error
	// GetSessionEvents retrieves events for a session
	GetSessionEvents(ctx context.Context, sessionID uuid.UUID, limit, offset int) ([]entities.SessionEvent, error)
	// CreateSubscription creates a new subscription
	CreateSubscription(ctx context.Context, subscription entities.SessionSubscription) error
	// GetSubscriptions retrieves subscriptions for a session
	GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error)
	// UpdateSubscription updates a subscription
	UpdateSubscription(ctx context.Context, subscription entities.SessionSubscription) error
	// DeleteSubscription deletes a subscription
	DeleteSubscription(ctx context.Context, id uuid.UUID) error
	// GetSubscriptionBySessionAndPhone gets a subscription by session ID and phone
	GetSubscriptionBySessionAndPhone(ctx context.Context, sessionID uuid.UUID, phone string) (entities.SessionSubscription, error)
	// GetPresences retrieves presence data for a session's subscriptions
	GetPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error)
	// GetCurrentPresences retrieves the latest presence status for all subscribed phones
	GetCurrentPresences(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceResponse, error)
	// GetPresenceHistory retrieves historical presence data with time range filtering
	GetPresenceHistory(ctx context.Context, sessionID uuid.UUID, req dtos.PresenceHistoryReq) (dtos.PresenceHistoryResponse, error)
	// FindActiveDeviceByRegID finds an active device by registration ID
	FindActiveDeviceByRegID(ctx context.Context, regID string) (dtos.DeviceIsActive, error)
	// CleanWhatsmeowTables removes data from Whatsmeow tables for a specific JID
	CleanWhatsmeowTables(ctx context.Context, ourJID string) error
	SubscribeIsExist(ctx context.Context, sessionId, phone string) bool
}

type sessionRepository struct {
	db *gorm.DB
}

// NewSessionRepository creates a new session repository
func NewRepository(db *gorm.DB) Repository {
	return &sessionRepository{
		db: db,
	}
}

func (r *sessionRepository) CreateSession(ctx context.Context, session entities.Session) (entities.Session, error) {
	err := r.db.WithContext(ctx).Create(&session).Error
	return session, err
}

func (r *sessionRepository) GetSessionByID(ctx context.Context, id uuid.UUID) (entities.Session, error) {
	var session entities.Session
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&session).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return session, errors.New("session not found")
		}
		return session, err
	}
	return session, nil
}

func (r *sessionRepository) GetSessionByRegID(ctx context.Context, regID string) (entities.Session, error) {
	var session entities.Session
	err := r.db.WithContext(ctx).Where("reg_id = ?", regID).First(&session).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return session, errors.New("session not found")
		}
		return session, err
	}
	return session, nil
}

func (r *sessionRepository) UpdateSession(ctx context.Context, session entities.Session) error {
	return r.db.WithContext(ctx).Save(&session).Error
}

func (r *sessionRepository) DeleteSession(ctx context.Context, id uuid.UUID) error {

	return r.db.WithContext(ctx).Delete(&entities.Session{}, id).Error
}

func (r *sessionRepository) ListSessions(ctx context.Context, status string, limit, offset int) ([]entities.Session, int64, error) {
	var sessions []entities.Session
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Session{})
	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	if limit > 0 {
		query = query.Limit(limit).Offset(offset)
	}

	err = query.Order("created_at DESC").Find(&sessions).Error
	return sessions, count, err
}

func (r *sessionRepository) RecordSessionEvent(ctx context.Context, event entities.SessionEvent) error {
	event.Timestamp = time.Now()
	return r.db.WithContext(ctx).Create(&event).Error
}

func (r *sessionRepository) GetSessionEvents(ctx context.Context, sessionID uuid.UUID, limit, offset int) ([]entities.SessionEvent, error) {
	var events []entities.SessionEvent
	query := r.db.WithContext(ctx).Where("session_id = ?", sessionID)

	if limit > 0 {
		query = query.Limit(limit).Offset(offset)
	}

	err := query.Order("timestamp DESC").Find(&events).Error
	return events, err
}

func (r *sessionRepository) CreateSubscription(ctx context.Context, subscription entities.SessionSubscription) error {
	return r.db.WithContext(ctx).Create(&subscription).Error
}

func (r *sessionRepository) GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error) {
	var subscriptions []entities.SessionSubscription
	err := r.db.WithContext(ctx).Where("session_id = ?", sessionID).Find(&subscriptions).Error
	return subscriptions, err
}

func (r *sessionRepository) UpdateSubscription(ctx context.Context, subscription entities.SessionSubscription) error {
	return r.db.WithContext(ctx).Save(&subscription).Error
}

func (r *sessionRepository) DeleteSubscription(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.SessionSubscription{}, id).Error
}

func (r *sessionRepository) GetSubscriptionBySessionAndPhone(ctx context.Context, sessionID uuid.UUID, phone string) (entities.SessionSubscription, error) {
	var subscription entities.SessionSubscription
	err := r.db.WithContext(ctx).
		Where("session_id = ? AND phone = ?", sessionID, phone).
		First(&subscription).Error
	return subscription, err
}

func (r *sessionRepository) GetPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error) {
	var presences []entities.Presence
	var count int64

	// First, get all subscriptions for this session
	var subscriptions []entities.SessionSubscription
	err := r.db.WithContext(ctx).Debug().Where("session_id = ?", sessionID).Find(&subscriptions).Error
	if err != nil {
		return dtos.PaginatedData{}, err
	}

	// If no subscriptions, return empty result
	if len(subscriptions) == 0 {
		return dtos.PaginatedData{
			Rows:       []entities.Presence{},
			Total:      0,
			Page:       int64(page),
			PerPage:    int64(perPage),
			TotalPages: 0,
		}, nil
	}

	// Get the session to find the reg_id
	var session entities.Session
	err = r.db.WithContext(ctx).Where("id = ?", sessionID).First(&session).Error
	if err != nil {
		return dtos.PaginatedData{}, err
	}

	// Extract phone numbers from subscriptions
	var phones []string
	for _, sub := range subscriptions {
		phones = append(phones, sub.Phone)
	}

	// Query presences for these phones and this session's reg_id
	query := r.db.WithContext(ctx).Model(&entities.Presence{}).
		Where("reg_id = ?", session.RegID).
		Where("subscribe_phone IN ?", phones).
		Order("created_at DESC")

	// Count total records
	err = query.Count(&count).Error
	if err != nil {
		return dtos.PaginatedData{}, err
	}

	// Apply pagination
	offset := (page - 1) * perPage
	if perPage > 0 {
		query = query.Limit(perPage).Offset(offset)
	}

	// Get the presences
	err = query.Find(&presences).Error
	if err != nil {
		return dtos.PaginatedData{}, err
	}

	return dtos.PaginatedData{
		Rows:       presences,
		Total:      count,
		Page:       int64(page),
		PerPage:    int64(perPage),
		TotalPages: int(math.Ceil(float64(count) / float64(perPage))),
	}, nil
}

func (r *sessionRepository) FindActiveDeviceByRegID(ctx context.Context, regID string) (dtos.DeviceIsActive, error) {
	var device dtos.DeviceIsActive
	err := r.db.WithContext(ctx).Select("jid as j_id, registration_id, platform, push_name").Table("whatsmeow_device").Where("registration_id = ?", regID).Find(&device).Error
	return device, err
}

// ...existing code...

// CleanWhatsmeowTables removes data from Whatsmeow tables for a specific JID
func (r *sessionRepository) CleanWhatsmeowTables(ctx context.Context, ourJID string) error {
	// Begin a transaction to ensure all deletions are performed atomically
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// Delete from whatsapp_contacts table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_contacts WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from whatsmeow_message_secrets table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_message_secrets WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from whatsmeow_sessions table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_sessions WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from whatsmeow_sender_keys table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_sender_keys WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from whatsmeow_privacy_tokens table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_privacy_tokens WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit the transaction
	return tx.Commit().Error
}

// ...existing code...

// CleanSession removes data related to a session from presences and session_subscriptions tables
func (r *sessionRepository) CleanSession(ctx context.Context, id string) error {
	// Begin a transaction to ensure all deletions are performed atomically
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// First, find the session ID from the reg_id
	var session entities.Session
	if err := tx.WithContext(ctx).Where("id = ?", id).First(&session).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("session not found")
		}
		return err
	}

	// Delete from presences table based on reg_id
	if err := tx.Exec("DELETE FROM presences WHERE reg_id = ?", session.RegID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from session_subscriptions table using the session ID
	if err := tx.Exec("DELETE FROM session_subscriptions WHERE session_id = ?", session.ID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit the transaction
	err := tx.Commit().Error

	r.CleanWhatsmeowTables(ctx, session.JID)
	return err

}

func (r *sessionRepository) GetCurrentPresences(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceResponse, error) {
	var response dtos.CurrentPresenceResponse
	response.SessionID = sessionID
	response.UpdatedAt = time.Now()

	// Get the session to find the reg_id
	var session entities.Session
	err := r.db.WithContext(ctx).Where("id = ?", sessionID).First(&session).Error
	if err != nil {
		return response, err
	}

	var presences []entities.Presence

	// If phone parameter is provided, get presence for that specific phone
	if phone != "" {
		err = r.db.WithContext(ctx).
			Where("reg_id = ? AND subscribe_phone = ?", session.RegID, phone).
			Order("created_at DESC").
			Limit(1).
			Find(&presences).Error
		if err != nil {
			return response, err
		}
	} else {
		// Original logic: get all subscriptions for this session
		var subscriptions []entities.SessionSubscription
		err := r.db.WithContext(ctx).Where("session_id = ?", sessionID).Find(&subscriptions).Error
		if err != nil {
			return response, err
		}

		// If no subscriptions, return empty result
		if len(subscriptions) == 0 {
			response.Presences = []dtos.CurrentPresenceItem{}
			return response, nil
		}

		// Extract phone numbers from subscriptions
		var phones []string
		for _, sub := range subscriptions {
			phones = append(phones, sub.Phone)
		}

		// Get the latest presence for each phone
		subQuery := r.db.WithContext(ctx).Model(&entities.Presence{}).
			Select("subscribe_phone, MAX(created_at) as max_created_at").
			Where("reg_id = ?", session.RegID).
			Where("subscribe_phone IN ?", phones).
			Group("subscribe_phone")

		err = r.db.WithContext(ctx).
			Table("presences").
			Select("presences.*").
			Joins("INNER JOIN (?) as latest ON presences.subscribe_phone = latest.subscribe_phone AND presences.created_at = latest.max_created_at", subQuery).
			Where("presences.reg_id = ?", session.RegID).
			Find(&presences).Error

		if err != nil {
			return response, err
		}
	}

	// Convert to response format
	response.Presences = make([]dtos.CurrentPresenceItem, len(presences))
	for i, presence := range presences {
		response.Presences[i] = dtos.CurrentPresenceItem{
			Phone:     presence.SubscribePhone,
			Status:    presence.Status,
			UpdatedAt: presence.CreatedAt,
		}
		if presence.LastSeen.IsZero() == false {
			response.Presences[i].LastSeen = &presence.LastSeen
		}
	}

	return response, nil
}

func (r *sessionRepository) GetPresenceHistory(ctx context.Context, sessionID uuid.UUID, req dtos.PresenceHistoryReq) (dtos.PresenceHistoryResponse, error) {
	var response dtos.PresenceHistoryResponse
	response.SessionID = sessionID
	response.TimeRange = req.TimeRange
	response.ToTime = time.Now()
	response.Page = req.Page
	response.PerPage = req.PerPage

	// Set default pagination values if not provided
	if response.Page <= 0 {
		response.Page = 1
	}
	if response.PerPage <= 0 {
		response.PerPage = 10
	}

	// Calculate the start time based on the time range
	switch req.TimeRange {
	case "3h":
		response.FromTime = response.ToTime.Add(-3 * time.Hour)
	case "24h":
		response.FromTime = response.ToTime.Add(-24 * time.Hour)
	case "2d":
		response.FromTime = response.ToTime.Add(-48 * time.Hour)
	case "3d":
		response.FromTime = response.ToTime.Add(-72 * time.Hour)
	default:
		return response, errors.New("invalid time range")
	}

	// Get the session to find the reg_id
	var session entities.Session
	err := r.db.WithContext(ctx).Where("id = ?", sessionID).First(&session).Error
	if err != nil {
		return response, err
	}

	// Build the base query
	query := r.db.WithContext(ctx).Model(&entities.Presence{}).
		Where("reg_id = ?", session.RegID).
		Where("created_at BETWEEN ? AND ?", response.FromTime, response.ToTime)

	// If phone parameter is provided, filter by that specific phone
	if req.Phone != "" {
		query = query.Where("subscribe_phone = ?", req.Phone)
	} else {
		// If no phone specified, get all subscriptions for this session
		var subscriptions []entities.SessionSubscription
		err := r.db.WithContext(ctx).Where("session_id = ?", sessionID).Find(&subscriptions).Error
		if err != nil {
			return response, err
		}

		// If no subscriptions, return empty result
		if len(subscriptions) == 0 {
			response.Presences = []dtos.PresenceHistoryItem{}
			response.Total = 0
			response.TotalPages = 0
			return response, nil
		}

		// Extract phone numbers from subscriptions
		var phones []string
		for _, sub := range subscriptions {
			phones = append(phones, sub.Phone)
		}

		query = query.Where("subscribe_phone IN ?", phones)
	}

	// Count total records
	var count int64
	err = query.Count(&count).Error
	if err != nil {
		return response, err
	}
	response.Total = count

	// Calculate total pages
	response.TotalPages = int((count + int64(response.PerPage) - 1) / int64(response.PerPage))

	// Calculate offset for pagination
	offset := (response.Page - 1) * response.PerPage

	// Get the presences with pagination
	var presences []entities.Presence
	err = query.Order("created_at DESC").
		Offset(offset).
		Limit(response.PerPage).
		Find(&presences).Error
	if err != nil {
		return response, err
	}

	// Convert to response format
	response.Presences = make([]dtos.PresenceHistoryItem, len(presences))
	for i, presence := range presences {
		response.Presences[i] = dtos.PresenceHistoryItem{
			Phone:     presence.SubscribePhone,
			Status:    presence.Status,
			Timestamp: presence.CreatedAt,
		}
		if presence.LastSeen.IsZero() == false {
			response.Presences[i].LastSeen = &presence.LastSeen
		}
	}

	return response, nil
}
